<template>
    <b-col :span="12">
        <NormAverage v-model:average="normalAverageScore" />
    </b-col>
    <b-col :span="12">
        <NormDifference v-model:difference="normalDeviation" />
    </b-col>
</template>
<script setup lang="ts">
import NormAverage from './norm-average.vue';
import NormDifference from './norm-difference.vue';
const normalDeviation = defineModel<any>('normalDeviation', {
    default: () => ({}),
});

const normalAverageScore = defineModel<any>('normalAverageScore', {
    default: () => ({}),
});
</script>
