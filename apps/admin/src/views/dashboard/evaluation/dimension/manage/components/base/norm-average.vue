<template>
    <b-form-item label="常模平均分" required asteriskPosition="end" field="normalAverageScore" :rules="[{ type: 'number', required: true, message: '请填写常模平均分' }]">
        <b-input-number v-model="average" placeholder="计分使用，请填写0-1000内数值，最多4为小数" hideButton :min="0" :max="1000" :precision="4" :formatter="scoreFormatter" />
    </b-form-item>
</template>
<script setup lang="ts">
import { scoreFormatter } from '@/utils/index';
const average = defineModel<any>('average', {
    default: () => ({}),
});
</script>
