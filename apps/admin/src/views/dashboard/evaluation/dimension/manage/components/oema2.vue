<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="24">
            <ParentDimension v-model:encryptParentId="formData.encryptParentId" :isEdit="isEdit" :productId="formData.productId" @change="changeParentProduct" />
        </b-col>
        <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
    </b-row>
</template>

<script setup lang="ts">
import ParentDimension from './base/parent-dimension.vue';
import Norm from './base/norm-layout.vue';

defineOptions({
    name: 'Oema2',
});

const props = defineProps({
    isEdit: {
        type: Boolean,
        default: false,
    },
});

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

const emit = defineEmits(['changeProduct']);

function changeParentProduct(value: any) {
    if (!value) {
        emit('changeProduct');
    }
}
</script>
