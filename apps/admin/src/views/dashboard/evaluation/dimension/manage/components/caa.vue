<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
    </b-row>
</template>

<script setup lang="ts">
import Norm from './base/norm-layout.vue';

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

defineOptions({
    name: 'Caa',
});
</script>
