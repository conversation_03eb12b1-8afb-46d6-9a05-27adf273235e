<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
        <b-col :span="24">
            <b-form-item
                label="维度说明-岗位匹配版"
                field="descriptionOf16T.1"
                asteriskPosition="end"
                :rules="[{ type: 'string', required: true, message: '请填写维度说明-岗位匹配版' }]"
            >
                <b-textarea v-model.trim="formData.descriptionOf16T[1]" placeholder="请填写" showWordLimit :maxLength="25" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-form-item label="维度说明-通用版" field="descriptionOf16T.2" asteriskPosition="end" :rules="[{ type: 'string', required: true, message: '请填写维度说明-通用版' }]">
                <b-input v-model.trim="formData.descriptionOf16T[2]" placeholder="请输入" showWordLimit :maxLength="5" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
        <b-col :span="12">
            <b-form-item
                label="维度倾向组"
                tooltip="四个维度倾向组代表：注意力方向（外倾、内倾）；认知方式（实感、直觉）；决策方式（理智、情感）；生活方式（判断、理解）"
                required
                asteriskPosition="end"
                field="dimensionTendency"
                :rules="[{ type: 'number', required: true, message: '请选择维度倾向组' }]"
            >
                <b-select v-model="formData.dimensionTendency" placeholder="请选择" :disabled="isEdit" @change="handleChangeDimensionTendency">
                    <b-option v-for="item in DIMENSIONAL_TENDENCY_GROUP_LIST" :key="item.value" :value="item.value">
                        {{ item.label }}
                    </b-option>
                </b-select>
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-table :tableData="formData.dimensionTendencyConfig" :columns="DIMENSIONAL_TENDENCY_GROUP_COLUMNS">
                <template #th-tendencyName> 倾向名称<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
                <template #th-tendencyDesc> 描述<span style="color: var(--danger-color-6); margin-left: 4px">*</span> </template>
                <template #td-tendency="{ raw }">
                    {{ raw.tendency === 'left' ? '左倾向' : '右倾向' }}
                </template>
                <template #td-tendencyName="{ raw }">
                    <b-input v-model.trim="raw.tendencyName" placeholder="请输入" :maxLength="5" showWordLimit />
                </template>
                <template #td-tendencyDesc="{ raw }">
                    <b-input v-model.trim="raw.tendencyDesc" placeholder="请输入" :maxLength="10" showWordLimit />
                </template>
            </b-table>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { DIMENSIONAL_TENDENCY_GROUP_COLUMNS, DIMENSIONAL_TENDENCY_GROUP_LIST } from '../constant';
import Norm from './base/norm-layout.vue';

defineOptions({
    name: 'TProfessional',
});

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

const props = defineProps({
    modelValue: {
        type: Object,
        default: () => ({}),
    },
    isEdit: {
        type: Boolean,
        default: false,
    },
});

function handleChangeDimensionTendency(value: any) {
    const tendencySymbolArray = value?.split('-') || [];
    formData.value.dimensionTendencyConfig[0].tendencySymbol = tendencySymbolArray[0];
    formData.value.dimensionTendencyConfig[1].tendencySymbol = tendencySymbolArray[1];
}

if (!props.isEdit) {
    formData.value.dimensionTendencyConfig = [
        {
            tendency: 'left',
            tendencySymbol: '',
            tendencyName: '',
            tendencyDesc: '',
        },
        {
            tendency: 'right',
            tendencySymbol: '',
            tendencyName: '',
            tendencyDesc: '',
        },
    ];
}
</script>
