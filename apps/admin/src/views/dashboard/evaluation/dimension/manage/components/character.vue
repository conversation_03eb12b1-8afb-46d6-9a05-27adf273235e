<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="24">
            <ParentDimension v-model:encryptParentId="formData.encryptParentId" :isEdit="isEdit" :productId="formData.productId" @change="changeParentProduct" />
        </b-col>
        <template v-if="formData.encryptParentId">
            <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
            <b-col :span="12">
                <b-form-item
                    label="建议作答时间"
                    required
                    asteriskPosition="end"
                    field="adviseAnswerTime"
                    :rules="[{ type: 'number', required: true, message: '请填写建议作答时间' }]"
                >
                    <b-input-number
                        v-model.trim="formData.adviseAnswerTime"
                        class="input-number-block-suffix"
                        placeholder="请填写30以内的数字，最多1位小数"
                        hideButton
                        :min="0.1"
                        :max="30"
                        :precision="1"
                    >
                        <template #suffix> 分钟 </template>
                    </b-input-number>
                </b-form-item>
                <div class="hint">
                    <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                    <span class="hint-text">考生答题时可见的提示时间</span>
                </div>
            </b-col>
            <b-col :span="12">
                <b-form-item
                    label="有效作答时间下限"
                    required
                    asteriskPosition="end"
                    field="minValidAnswerTime"
                    :rules="[{ type: 'number', required: true, message: '请填写有效作答时间下限' }, { validator: (value, callback) => formValidator(value, callback) }]"
                >
                    <b-input-number
                        v-model.trim="formData.minValidAnswerTime"
                        class="input-number-block-suffix"
                        placeholder="请填写30以内的数字，最多1位小数"
                        hideButton
                        :min="0.1"
                        :max="30"
                        :precision="1"
                    >
                        <template #suffix> 分钟 </template>
                    </b-input-number>
                </b-form-item>
                <div class="hint">
                    <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                    <span class="hint-text">报告参考性的判断条件</span>
                </div>
            </b-col>
        </template>
        <b-col v-if="!formData.encryptParentId" :span="24">
            <b-form-item label="维度说明" field="description" asteriskPosition="end" :rules="[{ type: 'string', required: true, message: '请填写维度说明' }]">
                <b-textarea v-model.trim="formData.description" placeholder="请填写" showWordLimit :maxLength="100" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import ParentDimension from './base/parent-dimension.vue';
import Norm from './base/norm-layout.vue';

defineOptions({
    name: 'Character',
});

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

const props = defineProps({
    isEdit: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['changeProduct']);

function changeParentProduct(value: any) {
    if (!value) {
        emit('changeProduct');
    }
}

function formValidator(value: any, callback: any) {
    if (formData.value.adviseAnswerTime < value) {
        callback('建议作答时间不应小于有效作答时间！');
        return;
    }
    callback();
}
</script>

<style scoped lang="less">
.parentId {
    :deep(.b-form-item-content) {
        max-width: 50%;
    }
}
</style>
