<template>
    <b-form-item label="常模标准差" required asteriskPosition="end" field="normalDeviation" :rules="[{ type: 'number', required: true, message: '请填写常模标准差' }]">
        <b-input-number v-model="difference" placeholder="请填写1000以内正数，最多4为小数" hideButton :min="0" :max="1000" :precision="4" :formatter="scoreFormatter" />
    </b-form-item>
</template>
<script setup lang="ts">
import { scoreFormatter } from '@/utils/index';
const difference = defineModel<any>('difference', {
    default: () => ({}),
});
</script>
