<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <b-col :span="24">
            <ParentDimension v-model:encryptParentId="formData.encryptParentId" :isEdit="isEdit" :productId="formData.productId" @change="changeParentProduct" />
        </b-col>
        <template v-if="formData.encryptParentId">
            <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
        </template>
    </b-row>
</template>

<script setup lang="ts">
import ParentDimension from './base/parent-dimension.vue';
import Norm from './base/norm-layout.vue';
defineOptions({
    name: 'ProfessionalWillfulness',
});
const props = defineProps({
    isEdit: {
        type: Boolean,
        default: false,
    },
});

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

const emit = defineEmits(['changeProduct']);

function changeParentProduct(value: any) {
    if (!value) {
        emit('changeProduct');
    }
}
</script>

<style scoped lang="less">
.parentId {
    :deep(.b-form-item-content) {
        max-width: 50%;
    }
}
</style>
