<template>
    <b-row :gutter="[20, 24]" style="margin-left: 0; margin-right: 0">
        <Norm v-model:normalAverageScore="formData.normalAverageScore" v-model:normalDeviation="formData.normalDeviation" />
        <b-col :span="12">
            <b-form-item label="建议作答时间" required asteriskPosition="end" field="adviseAnswerTime" :rules="[{ type: 'number', required: true, message: '请填写建议作答时间' }]">
                <b-input-number
                    v-model.trim="formData.adviseAnswerTime"
                    class="input-number-block-suffix"
                    placeholder="请填写30以内的数字，最多1位小数"
                    hideButton
                    :min="0.1"
                    :max="30"
                    :precision="1"
                >
                    <template #suffix> 分钟 </template>
                </b-input-number>
            </b-form-item>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">考生答题时可见的提示时间</span>
            </div>
        </b-col>
        <b-col :span="12">
            <b-row :gutter="[20, 24]" class="answerT-time-group">
                <b-col class="min-valid-answer-time" :span="13">
                    <b-form-item
                        label="有效作答时间范围"
                        required
                        asteriskPosition="end"
                        field="minValidAnswerTime"
                        :rules="[
                            { type: 'number', required: true, message: '请填写有效作答时间范围' },
                            { validator: (value: any, callback: any) => formValidator(value, callback, 'minValidAnswerTime') },
                        ]"
                    >
                        <b-input-number
                            v-model.trim="formData.minValidAnswerTime"
                            class="input-number-block-suffix"
                            placeholder="请填写"
                            hideButton
                            :min="0.1"
                            :max="30"
                            :precision="1"
                            @blur="rangeBlur"
                        >
                            <template #suffix> 分钟 </template>
                        </b-input-number>
                        <div class="range-separator" />
                    </b-form-item>
                </b-col>
                <b-col :span="11" class="max-valid-answer-time">
                    <b-form-item
                        required
                        asteriskPosition="end"
                        field="maxValidAnswerTime"
                        :rules="[
                            { type: 'number', required: true, message: '请填写有效作答时间范围' },
                            { validator: (value: any, callback: any) => formValidator(value, callback, 'maxValidAnswerTime') },
                        ]"
                    >
                        <b-input-number
                            v-model.trim="formData.maxValidAnswerTime"
                            class="input-number-block-suffix"
                            placeholder="请填写"
                            hideButton
                            :min="0.1"
                            :max="30"
                            :precision="1"
                            @blur="rangeBlur"
                        >
                            <template #suffix> 分钟 </template>
                        </b-input-number>
                    </b-form-item>
                </b-col>
            </b-row>
            <div class="hint">
                <SvgIcon name="hicon-alert" width="16" height="16" class="icon" />
                <span class="hint-text">报告参考性的判断条件</span>
            </div>
        </b-col>
        <b-col :span="24">
            <b-form-item label="适配人群" required asteriskPosition="end" field="crowd" :rules="[{ type: 'array', required: true, message: '请选择适配人群' }]">
                <b-select v-model="formData.crowd" multiple :options="CROWD_LIST" placeholder="请选择" />
            </b-form-item>
        </b-col>
        <b-col :span="24">
            <b-form-item label="维度说明" field="description" asteriskPosition="end">
                <b-textarea v-model.trim="formData.description" placeholder="请填写" showWordLimit :maxLength="100" :showResize="false" :autoSize="{ minRows: 2 }" />
            </b-form-item>
        </b-col>
    </b-row>
</template>

<script setup lang="ts">
import { CROWD_LIST } from '../constant';
import Norm from './base/norm-layout.vue';

defineOptions({
    name: 'BaseInfo',
});

const formData = defineModel<any>('formData', {
    default: () => ({}),
});

function rangeBlur() {
    const { minValidAnswerTime, maxValidAnswerTime } = formData.value;
    if (minValidAnswerTime && maxValidAnswerTime) {
        const min = Math.min(minValidAnswerTime, maxValidAnswerTime);
        const max = Math.max(minValidAnswerTime, maxValidAnswerTime);
        formData.value.minValidAnswerTime = min;
        formData.value.maxValidAnswerTime = max;
    }
}
function formValidator(value: any, callback: any, field: any) {
    const { minValidAnswerTime, maxValidAnswerTime, adviseAnswerTime } = formData.value;
    const isMax = field === 'maxValidAnswerTime' && value <= adviseAnswerTime;
    const isMin = field === 'minValidAnswerTime' && value >= adviseAnswerTime;

    if (minValidAnswerTime >= 0 && maxValidAnswerTime >= 0 && (isMax || isMin)) {
        callback('建议作答时间必须在有效作答时间范围内');
        return;
    }
    callback();
}
</script>
