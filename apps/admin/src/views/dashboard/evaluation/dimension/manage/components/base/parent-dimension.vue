<template>
    <b-form-item label="上级维度" asteriskPosition="end" class="parentId">
        <b-select
            v-model="encryptParentId"
            :options="parentDimensionList"
            placeholder="请选择"
            :disabled="isEdit"
            allowClear
            allowSearch
            :fallbackOption="fallback"
            @change="handleChange"
        />
    </b-form-item>
</template>

<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { _dimensionOption } from '@/services/api/dimension';

const encryptParentId = defineModel<any>('encryptParentId', {
    default: () => undefined,
});

const parentDimensionList = ref([]);

const props = defineProps({
    isEdit: {
        type: Boolean,
        default: false,
    },
    productId: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['changeProduct', 'change']);

function fallback(value: string) {
    return {
        value,
        label: '请选择',
    };
}

const handleChange = (value: any) => {
    emit('change', value);
};

async function getEvaluationDimensionOption() {
    try {
        const params = {
            dimensionStatus: 0, // 0-启用;1-停用
            productId: props.productId,
            dimensionLevel: 1,
        };
        const { code, data } = await _dimensionOption(params);
        if (code === 0) {
            parentDimensionList.value =
                data?.map((x: any) => ({
                    ...x,
                    value: x.encryptId,
                    label: x.name,
                })) || [];
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}
watchEffect(() => {
    getEvaluationDimensionOption();
});
</script>
